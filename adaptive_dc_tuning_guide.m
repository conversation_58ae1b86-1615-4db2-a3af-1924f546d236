%% Adaptive DC Tuning Guide for Original Method (Method 4)
% 기존 방법의 성능을 최적화하기 위한 파라미터 튜닝 가이드

clear all;
close all;
clc;

fprintf('=== Adaptive DC Original Method Tuning Guide ===\n\n');

%% 1. 기본 파라미터 설명
fprintf('1. 기본 파라미터 설명:\n');
fprintf('   DC_BASE_ALPHA: 기본 알파값 (0.7~0.95 권장)\n');
fprintf('     - 높을수록: 빠른 적응, 노이즈에 민감\n');
fprintf('     - 낮을수록: 느린 적응, 안정적\n');
fprintf('     - 현재값: 0.850 (권장 시작값)\n\n');

fprintf('   DC_ALPHA_MIN/MAX: 알파값 범위 제한\n');
fprintf('     - MIN: 0.750 (너무 낮으면 적응 불가)\n');
fprintf('     - MAX: 0.950 (너무 높으면 불안정)\n\n');

fprintf('   DC_STABILITY_THRESHOLD: DC 안정성 판단 임계값\n');
fprintf('     - 낮을수록: 더 민감한 안정성 판단\n');
fprintf('     - 높을수록: 더 관대한 안정성 판단\n');
fprintf('     - 현재값: 0.001\n\n');

%% 2. 튜닝 전략
fprintf('2. 튜닝 전략:\n\n');

fprintf('   A. 수신율이 낮은 경우:\n');
fprintf('      - DC_BASE_ALPHA를 0.820~0.870으로 낮춤\n');
fprintf('      - DC_STABILITY_THRESHOLD를 0.002로 높임\n');
fprintf('      - 더 안정적인 DC 추적\n\n');

fprintf('   B. 수신율은 좋지만 노이즈가 많은 경우:\n');
fprintf('      - DC_BASE_ALPHA를 0.880~0.920으로 높임\n');
fprintf('      - DC_STABILITY_THRESHOLD를 0.0005로 낮춤\n');
fprintf('      - 빠른 적응으로 노이즈 대응\n\n');

fprintf('   C. 신호가 매우 약한 경우:\n');
fprintf('      - DC_ALPHA_MIN을 0.700으로 낮춤\n');
fprintf('      - DC_BASE_ALPHA를 0.800으로 낮춤\n');
fprintf('      - 매우 보수적인 DC 추적\n\n');

%% 3. 자동 튜닝 함수
function optimal_params = auto_tune_dc_params(test_results)
    % 테스트 결과를 기반으로 최적 파라미터 제안
    
    success_rate = test_results.packets_received / ...
                  (test_results.packets_received + test_results.crc_errors) * 100;
    
    if success_rate < 80
        % 수신율이 낮음 - 안정성 우선
        optimal_params.DC_BASE_ALPHA = 0.820;
        optimal_params.DC_STABILITY_THRESHOLD = 0.002;
        optimal_params.DC_ALPHA_MIN = 0.700;
        optimal_params.strategy = 'Stability Priority';
        
    elseif success_rate < 90
        % 보통 수신율 - 균형
        optimal_params.DC_BASE_ALPHA = 0.850;
        optimal_params.DC_STABILITY_THRESHOLD = 0.001;
        optimal_params.DC_ALPHA_MIN = 0.750;
        optimal_params.strategy = 'Balanced';
        
    else
        % 높은 수신율 - 성능 최적화
        optimal_params.DC_BASE_ALPHA = 0.880;
        optimal_params.DC_STABILITY_THRESHOLD = 0.0008;
        optimal_params.DC_ALPHA_MIN = 0.770;
        optimal_params.strategy = 'Performance Optimized';
    end
    
    optimal_params.DC_ALPHA_MAX = 0.950;  % 고정값
    optimal_params.DC_VARIANCE_WINDOW = 20;  % 고정값
end

%% 4. 파라미터 테스트 함수
function run_parameter_test(base_alpha_values, stability_thresholds)
    fprintf('4. 파라미터 테스트 실행:\n\n');
    
    results = [];
    test_count = 0;
    
    for alpha = base_alpha_values
        for threshold = stability_thresholds
            test_count = test_count + 1;
            fprintf('Test %d: BASE_ALPHA=%.3f, STABILITY_THRESHOLD=%.4f\n', ...
                    test_count, alpha, threshold);
            
            % 여기서 실제 테스트를 실행하고 결과를 수집
            % (실제 구현에서는 메인 스크립트를 수정하여 실행)
            
            % 예시 결과 (실제로는 테스트 실행 결과)
            test_result.packets_received = randi([30, 50]);
            test_result.crc_errors = randi([5, 15]);
            test_result.base_alpha = alpha;
            test_result.stability_threshold = threshold;
            
            results = [results; test_result];
            
            success_rate = test_result.packets_received / ...
                          (test_result.packets_received + test_result.crc_errors) * 100;
            fprintf('   Success Rate: %.2f%%\n\n', success_rate);
        end
    end
    
    % 최고 성능 파라미터 찾기
    success_rates = [];
    for i = 1:length(results)
        success_rates(i) = results(i).packets_received / ...
                          (results(i).packets_received + results(i).crc_errors) * 100;
    end
    
    [best_rate, best_idx] = max(success_rates);
    best_params = results(best_idx);
    
    fprintf('최고 성능 파라미터:\n');
    fprintf('   BASE_ALPHA: %.3f\n', best_params.base_alpha);
    fprintf('   STABILITY_THRESHOLD: %.4f\n', best_params.stability_threshold);
    fprintf('   Success Rate: %.2f%%\n\n', best_rate);
end

%% 5. 실행 예시
fprintf('5. 튜닝 실행 예시:\n\n');

% 테스트할 파라미터 범위
alpha_values = [0.820, 0.850, 0.880];
threshold_values = [0.0008, 0.001, 0.002];

fprintf('다음 명령으로 파라미터 테스트 실행:\n');
fprintf('run_parameter_test([0.820, 0.850, 0.880], [0.0008, 0.001, 0.002]);\n\n');

% 실제 테스트 실행 (주석 해제하여 사용)
% run_parameter_test(alpha_values, threshold_values);

%% 6. 수동 튜닝 가이드
fprintf('6. 수동 튜닝 단계:\n\n');
fprintf('   Step 1: 현재 성능 측정\n');
fprintf('           - 기본 설정으로 실행하여 기준 성능 확인\n\n');

fprintf('   Step 2: BASE_ALPHA 조정\n');
fprintf('           - 수신율이 낮으면 0.820으로 낮춤\n');
fprintf('           - 수신율이 높으면 0.880으로 높임\n\n');

fprintf('   Step 3: STABILITY_THRESHOLD 미세 조정\n');
fprintf('           - 너무 민감하면 0.002로 높임\n');
fprintf('           - 너무 둔감하면 0.0008로 낮춤\n\n');

fprintf('   Step 4: 결과 확인 및 반복\n');
fprintf('           - 각 변경 후 성능 측정\n');
fprintf('           - 최적값 찾을 때까지 반복\n\n');

fprintf('=== 튜닝 가이드 완료 ===\n');
