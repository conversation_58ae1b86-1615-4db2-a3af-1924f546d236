%% AIS 입력 데이터 주파수 분석 도구
% 입력 데이터의 주파수 특성을 상세히 분석하는 독립 실행 도구

clear all;
close all;
clc;

fprintf('=== AIS Input Data Frequency Analysis Tool ===\n\n');

%% 설정 파라미터
BIT_RATE = 9600;        % AIS 비트율
OSR = 10;               % 오버샘플링 비율
sampling_freq = BIT_RATE * OSR;  % 샘플링 주파수

% 분석 설정
ANALYSIS_LENGTH = 50000;    % 분석할 샘플 수
ENABLE_DETAILED_PLOT = 1;   % 상세 플롯 활성화
ENABLE_FILTER_ANALYSIS = 1; % 필터 분석 활성화

%% 데이터 로드
fprintf('Loading input data...\n');

% 데이터 파일 선택
USE_CHx_RAW_DATA = 0;  % 0: Ch1, 1: Ch2
ADC_SUB_DC_OFFSET = 1450;
ADC_MAX_VALUE = 2048;

if (USE_CHx_RAW_DATA == 0)
    data_file = fopen('./DumpData/DUMPDATA_250525_ch1.bin');
else
    data_file = fopen('./DumpData/DUMPDATA_250525_ch2.bin');
end

if data_file == -1
    error('Cannot open data file. Please check file path.');
end

raw_data = fread(data_file, 'uint16');
fclose(data_file);

% 데이터 전처리
processed_data = (raw_data - ADC_SUB_DC_OFFSET) / ADC_MAX_VALUE;

fprintf('Data loaded: %d samples\n', length(processed_data));
fprintf('Sampling frequency: %.1f Hz\n', sampling_freq);
fprintf('Data duration: %.2f seconds\n\n', length(processed_data)/sampling_freq);

%% 분석할 데이터 세그먼트 선택
analysis_length = min(ANALYSIS_LENGTH, length(processed_data));
analysis_data = processed_data(1:analysis_length);

%% 기본 신호 통계
fprintf('=== Basic Signal Statistics ===\n');
fprintf('Mean: %.6f\n', mean(analysis_data));
fprintf('Standard Deviation: %.6f\n', std(analysis_data));
fprintf('RMS: %.6f\n', rms(analysis_data));
fprintf('Peak-to-Peak: %.6f\n', max(analysis_data) - min(analysis_data));
fprintf('Dynamic Range: %.2f dB\n', 20*log10((max(analysis_data) - min(analysis_data))/std(analysis_data)));

%% FFT 기반 주파수 분석
fprintf('\n=== FFT Analysis ===\n');

N = length(analysis_data);
window = hanning(N)';
windowed_data = analysis_data .* window;

% FFT 계산
Y = fft(windowed_data);
P2 = abs(Y/N);
P1 = P2(1:N/2+1);
P1(2:end-1) = 2*P1(2:end-1);

% 주파수 축
f = sampling_freq*(0:(N/2))/N;

% 피크 찾기
[peak_values, peak_indices] = findpeaks(P1, 'MinPeakHeight', max(P1)*0.1, 'NPeaks', 5);
peak_frequencies = f(peak_indices);

fprintf('Top 5 frequency peaks:\n');
for i = 1:length(peak_frequencies)
    fprintf('  %.1f Hz (%.2f dB)\n', peak_frequencies(i), 20*log10(peak_values(i)));
end

% 전력 스펙트럼 밀도
psd = P1.^2;
total_power = sum(psd);
fprintf('Total signal power: %.6f\n', total_power);

%% 대역별 전력 분석
fprintf('\n=== Band Power Analysis ===\n');

% AIS 관련 주파수 대역 정의
bands = struct();
bands.dc = [0, 100];                    % DC ~ 100Hz
bands.low_freq = [100, 1000];           % 100Hz ~ 1kHz
bands.ais_fundamental = [1000, 5000];   % 1kHz ~ 5kHz (AIS 기본 대역)
bands.ais_harmonic = [5000, 20000];     % 5kHz ~ 20kHz (고조파)
bands.high_freq = [20000, sampling_freq/2]; % 20kHz ~ Nyquist

band_names = fieldnames(bands);
for i = 1:length(band_names)
    band_name = band_names{i};
    freq_range = bands.(band_name);
    
    % 해당 대역의 인덱스 찾기
    band_idx = find(f >= freq_range(1) & f <= freq_range(2));
    band_power = sum(psd(band_idx));
    band_percentage = band_power / total_power * 100;
    
    fprintf('%s (%.0f-%.0f Hz): %.2f%% of total power\n', ...
            band_name, freq_range(1), freq_range(2), band_percentage);
end

%% 플롯 생성
if ENABLE_DETAILED_PLOT
    % Figure 1: 시간 도메인 신호
    figure(1);
    subplot(2,1,1);
    t = (0:analysis_length-1) / sampling_freq * 1000; % ms 단위
    plot(t, analysis_data);
    title('Time Domain Signal');
    xlabel('Time (ms)');
    ylabel('Amplitude');
    grid on;
    xlim([0 min(100, t(end))]);
    
    subplot(2,1,2);
    histogram(analysis_data, 50);
    title('Amplitude Distribution');
    xlabel('Amplitude');
    ylabel('Count');
    grid on;
    
    % Figure 2: 주파수 스펙트럼
    figure(2);
    subplot(2,1,1);
    plot(f/1000, 20*log10(P1));
    title('Frequency Spectrum (dB scale)');
    xlabel('Frequency (kHz)');
    ylabel('Magnitude (dB)');
    grid on;
    xlim([0 sampling_freq/2000]);
    
    % 피크 표시
    hold on;
    plot(peak_frequencies/1000, 20*log10(peak_values), 'ro', 'MarkerSize', 8);
    for i = 1:length(peak_frequencies)
        text(peak_frequencies(i)/1000, 20*log10(peak_values(i))+2, ...
             sprintf('%.1f Hz', peak_frequencies(i)), 'FontSize', 8);
    end
    hold off;
    
    subplot(2,1,2);
    plot(f/1000, P1);
    title('Frequency Spectrum (Linear scale)');
    xlabel('Frequency (kHz)');
    ylabel('Magnitude');
    grid on;
    xlim([0 sampling_freq/2000]);
    
    % Figure 3: 스펙트로그램
    figure(3);
    window_length = min(512, floor(N/8));
    overlap = floor(window_length * 0.75);
    nfft = max(512, 2^nextpow2(window_length));
    
    spectrogram(analysis_data, window_length, overlap, nfft, sampling_freq, 'yaxis');
    title('Spectrogram');
    colorbar;
    ylim([0 sampling_freq/2000]);
    
    % Figure 4: 대역별 전력 분포
    figure(4);
    band_powers = [];
    band_labels = {};
    for i = 1:length(band_names)
        band_name = band_names{i};
        freq_range = bands.(band_name);
        band_idx = find(f >= freq_range(1) & f <= freq_range(2));
        band_power = sum(psd(band_idx));
        band_powers = [band_powers, band_power/total_power*100];
        band_labels{end+1} = sprintf('%s\n(%.0f-%.0f Hz)', band_name, freq_range(1), freq_range(2));
    end
    
    pie(band_powers, band_labels);
    title('Power Distribution by Frequency Bands');
end

%% 필터 효과 분석 (옵션)
if ENABLE_FILTER_ANALYSIS
    fprintf('\n=== Filter Analysis ===\n');
    
    % 간단한 저역통과 필터 적용 (예시)
    cutoff_freq = 5000; % Hz
    normalized_cutoff = cutoff_freq / (sampling_freq/2);
    [b, a] = butter(4, normalized_cutoff, 'low');
    
    filtered_data = filtfilt(b, a, analysis_data);
    
    % 필터링된 데이터의 FFT
    Y_filtered = fft(filtered_data .* window);
    P1_filtered = abs(Y_filtered/N);
    P1_filtered = P1_filtered(1:N/2+1);
    P1_filtered(2:end-1) = 2*P1_filtered(2:end-1);
    
    % 필터 효과 비교
    figure(5);
    subplot(2,1,1);
    plot(f/1000, 20*log10(P1), 'b-', 'LineWidth', 1.5);
    hold on;
    plot(f/1000, 20*log10(P1_filtered), 'r-', 'LineWidth', 1.5);
    hold off;
    title('Filter Effect Comparison');
    xlabel('Frequency (kHz)');
    ylabel('Magnitude (dB)');
    legend('Original', 'Filtered', 'Location', 'best');
    grid on;
    xlim([0 sampling_freq/2000]);
    
    subplot(2,1,2);
    t_short = (0:min(5000, length(analysis_data))-1) / sampling_freq * 1000;
    plot(t_short, analysis_data(1:length(t_short)), 'b-');
    hold on;
    plot(t_short, filtered_data(1:length(t_short)), 'r-');
    hold off;
    title('Time Domain Comparison');
    xlabel('Time (ms)');
    ylabel('Amplitude');
    legend('Original', 'Filtered', 'Location', 'best');
    grid on;
    
    % 필터 성능 평가
    noise_reduction = 20*log10(rms(analysis_data)/rms(filtered_data));
    fprintf('Noise reduction: %.2f dB\n', noise_reduction);
    
    % 신호 대 노이즈 비 개선
    signal_band_idx = find(f >= 1000 & f <= 5000);
    noise_band_idx = find(f >= 20000);
    
    if ~isempty(noise_band_idx)
        snr_original = 10*log10(sum(psd(signal_band_idx))/sum(psd(noise_band_idx)));
        psd_filtered = P1_filtered.^2;
        snr_filtered = 10*log10(sum(psd_filtered(signal_band_idx))/sum(psd_filtered(noise_band_idx)));
        snr_improvement = snr_filtered - snr_original;
        
        fprintf('Original SNR: %.2f dB\n', snr_original);
        fprintf('Filtered SNR: %.2f dB\n', snr_filtered);
        fprintf('SNR improvement: %.2f dB\n', snr_improvement);
    end
end

fprintf('\n=== Analysis Complete ===\n');
fprintf('Generated plots:\n');
fprintf('  Figure 1: Time domain signal and amplitude distribution\n');
fprintf('  Figure 2: Frequency spectrum\n');
fprintf('  Figure 3: Spectrogram\n');
fprintf('  Figure 4: Power distribution by frequency bands\n');
if ENABLE_FILTER_ANALYSIS
    fprintf('  Figure 5: Filter effect comparison\n');
end
