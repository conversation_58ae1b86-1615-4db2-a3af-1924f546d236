%% Adaptive DC Performance Test Script
% 다양한 DC 방법의 성능을 비교하는 스크립트

clear all;
close all;
clc;

fprintf('=== Adaptive DC Performance Comparison Test ===\n\n');

% 테스트할 DC 방법들
dc_methods = [1, 2, 3, 4];  % <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>F, Original
method_names = {'Enhanced', 'Kalman', 'HPF', 'Original'};
results = struct();

% 각 방법에 대해 테스트 실행
for i = 1:length(dc_methods)
    fprintf('Testing DC Method %d (%s)...\n', dc_methods(i), method_names{i});
    
    % 메인 스크립트의 설정 변경
    % 임시 파일 생성하여 설정 변경
    temp_file = 'temp_ais_receiver_test.m';
    
    % 원본 파일 읽기
    fid_orig = fopen('ais_receiver_collation_with_real_data_250709.m', 'r');
    content = fread(fid_orig, '*char')';
    fclose(fid_orig);
    
    % ADAPTIVE_DC_METHOD 설정 변경
    pattern = 'ADAPTIVE_DC_METHOD\s*=\s*\d+;';
    replacement = sprintf('ADAPTIVE_DC_METHOD      = %d;', dc_methods(i));
    modified_content = regexprep(content, pattern, replacement);
    
    % 임시 파일에 저장
    fid_temp = fopen(temp_file, 'w');
    fwrite(fid_temp, modified_content);
    fclose(fid_temp);
    
    % 테스트 실행
    try
        % 출력 캡처를 위한 diary 시작
        diary_file = sprintf('dc_test_output_%d.txt', i);
        diary(diary_file);
        
        % 임시 스크립트 실행
        run(temp_file);
        
        % diary 종료
        diary off;
        
        % 결과 저장 (전역 변수에서 가져옴)
        results(i).method = method_names{i};
        results(i).packets_received = evalin('base', 'G_dRcvPktCnt');
        results(i).crc_errors = evalin('base', 'G_dCrcErrCnt');
        results(i).dc_updates = evalin('base', 'G_DcUpdateCount');
        
        % 성공률 계산
        total_attempts = results(i).packets_received + results(i).crc_errors;
        if total_attempts > 0
            results(i).success_rate = results(i).packets_received / total_attempts * 100;
        else
            results(i).success_rate = 0;
        end
        
        fprintf('  Packets Received: %d\n', results(i).packets_received);
        fprintf('  Success Rate: %.2f%%\n', results(i).success_rate);
        fprintf('  DC Updates: %d\n\n', results(i).dc_updates);
        
    catch ME
        fprintf('Error testing method %s: %s\n\n', method_names{i}, ME.message);
        results(i).method = method_names{i};
        results(i).packets_received = 0;
        results(i).crc_errors = 0;
        results(i).dc_updates = 0;
        results(i).success_rate = 0;
    end
    
    % 임시 파일 삭제
    if exist(temp_file, 'file')
        delete(temp_file);
    end
    
    % 변수 정리
    clear global;
    evalin('base', 'clear all');
end

% 결과 비교 및 시각화
fprintf('\n=== Performance Comparison Results ===\n');
fprintf('Method\t\tPackets\tSuccess%%\tDC Updates\n');
fprintf('------\t\t-------\t--------\t----------\n');

best_method = 1;
best_success_rate = 0;

for i = 1:length(results)
    fprintf('%s\t\t%d\t%.2f%%\t\t%d\n', ...
        results(i).method, ...
        results(i).packets_received, ...
        results(i).success_rate, ...
        results(i).dc_updates);
    
    if results(i).success_rate > best_success_rate
        best_success_rate = results(i).success_rate;
        best_method = i;
    end
end

fprintf('\nBest performing method: %s (%.2f%% success rate)\n', ...
    results(best_method).method, best_success_rate);

% 성능 비교 그래프
figure(100);
subplot(2,2,1);
bar([results.packets_received]);
title('Packets Received');
set(gca, 'XTickLabel', {results.method});
ylabel('Count');

subplot(2,2,2);
bar([results.success_rate]);
title('Success Rate');
set(gca, 'XTickLabel', {results.method});
ylabel('Percentage (%)');

subplot(2,2,3);
bar([results.dc_updates]);
title('DC Updates');
set(gca, 'XTickLabel', {results.method});
ylabel('Count');

subplot(2,2,4);
bar([results.crc_errors]);
title('CRC Errors');
set(gca, 'XTickLabel', {results.method});
ylabel('Count');

sgtitle('Adaptive DC Method Performance Comparison');

% 결과를 MAT 파일로 저장
save('dc_performance_results.mat', 'results');
fprintf('\nResults saved to dc_performance_results.mat\n');

fprintf('\n=== Test Complete ===\n');
